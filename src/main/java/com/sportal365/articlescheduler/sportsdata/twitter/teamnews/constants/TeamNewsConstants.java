package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.constants;

import java.util.List;

public class TeamNewsConstants {

    public static final String XAI_MODEL = "grok-3-latest";
    public static final String SEARCH_MODE_ON = "on";
    public static final String SOURCE_TYPE_X = "x";
    public static final String SOURCE_TYPE_WEB = "web";
    public static final String SOURCE_TYPE_NEWS = "news";
    
    public static final String SYSTEM_ROLE = "system";
    public static final String USER_ROLE = "user";
    
    public static final int DEFAULT_TEMPERATURE = 0;
    public static final int DEFAULT_MAX_TOKENS = 1500;
    public static final int DEFAULT_MAX_SEARCH_RESULTS = 20;
    public static final boolean DEFAULT_STREAM = false;
    public static final boolean DEFAULT_RETURN_CITATIONS = true;
    
    public static final List<String> DEFAULT_X_HANDLES = List.of("UEFA", "FIFAcom");

    public static final String SYSTEM_PROMPT =
            "You are a sports news assistant with access to live X/Twitter search capabilities. " +
                    "When searching for match news, prioritize posts from verified sports accounts, official team accounts, " +
                    "and credible football journalists. Always indicate the source and recency of information found through live search. " +
                    "Format your response in clean Markdown with proper headings, bullet points, and clear paragraph breaks.";

    public static final String USER_PROMPT_TEMPLATE =
            "Please provide comprehensive sports journalism coverage for the upcoming %s match between %s and %s " +
                    "in the %s tournament scheduled for %s. The event will take place at %s with %s officiating as referee. " +
                    "This is a %s round match in %s for the sport of %s. Please respond in %s language.\n\n" +

                    "Provide detailed coverage including:\n" +
                    "- Latest team news\n" +
                    "- Manager quotes with tactical insights and match preparation comments\n" +
                    "- Player quotes, interviews, and pre-match statements\n" +

                    "Format your response using clean, professional Markdown structure:\n\n" +
                    "## Team News\n" +
                    "%s \n" +
                    " %s \n\n" +
                    "## Manager Quotes\n" +
                    "%s \n" +
                    "%s \n\n" +
                    "## Player Quotes\n" +
                    "%s Players\n" +
                    "%s Players\n\n" +
                    "## Match Preview\n" +
                    "## citations\n" +
                    "Use proper Markdown formatting with clear headings, bullet points, bold text for emphasis, " +
                    "and include source citations where available. Ensure all content is factual, recent, and relevant.";

    // Logging messages
    public static final String CLIENT_LOG_MESSAGE_SUCCESS = "X.AI API call completed successfully";
    public static final String CLIENT_LOG_MESSAGE_FAILURE = "X.AI API call failed: {}";

    // Multi-language section headers
    public static final String[] TEAM_NEWS_HEADERS = {
        "## Team News", "## News", "## Новини за отборите", "## Новини",
        "## Noticias del Equipo", "## Nouvelles de l'équipe", "## Teamnachrichten"
    };

    public static final String[] MANAGER_QUOTES_HEADERS = {
        "## Manager Quotes", "## Цитати от мениджърите", "## Цитати от треньорите",
        "## Citas del Entrenador", "## Citations de l'entraîneur", "## Trainer-Zitate"
    };

    public static final String[] PLAYER_QUOTES_HEADERS = {
        "## Player Quotes", "## Цитати от играчите",
        "## Citas de Jugadores", "## Citations de joueurs", "## Spieler-Zitate"
    };

    // Multi-language keywords for news categorization
    public static final String[] INJURY_KEYWORDS = {
        "injur", "hurt", "fitness", "контузия", "травма", "наранявания", "фитнес",
        "lesión", "herida", "blessure", "verletzung", "infortunio"
    };

    public static final String[] SUSPENSION_KEYWORDS = {
        "suspend", "banned", "card", "наказание", "картони", "дисквалификация",
        "suspendido", "tarjeta", "suspendu", "carte", "gesperrt", "karte"
    };

    public static final String[] RETURN_KEYWORDS = {
        "return", "back", "recover", "връщане", "възстановяване", "обратно",
        "regreso", "vuelta", "retour", "récupérer", "rückkehr", "zurück"
    };

    public static final String[] KEY_PLAYER_KEYWORDS = {
        "key", "star", "important", "ключов", "звезда", "важен",
        "clave", "estrella", "importante", "clé", "étoile", "schlüssel", "stern"
    };
}
