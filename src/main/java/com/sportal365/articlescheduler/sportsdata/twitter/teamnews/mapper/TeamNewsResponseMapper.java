package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.mapper;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.model.TeamNewsData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TeamNewsResponseMapper {

    public MatchDetails.TeamNews mapToMatchDetailsTeamNews(TeamNewsData teamNewsData) {
        try {
            String content = teamNewsData.getMarkdownContent();
            if (content == null || content.isEmpty()) {
                return null;
            }

            // Extract the first ## section (Team News)
            String teamNewsContent = extractSectionContent(content, 0);

            if (teamNewsContent == null || teamNewsContent.trim().isEmpty()) {
                return null;
            }

            return MatchDetails.TeamNews.builder()
                    .teamNews(teamNewsContent.trim())
                    .build();

        } catch (Exception e) {
            log.error("Error mapping team news data: {}", e.getMessage(), e);
            return null;
        }
    }

    public MatchDetails.Quotes mapToMatchDetailsQuotes(TeamNewsData teamNewsData) {
        try {
            String content = teamNewsData.getMarkdownContent();
            if (content == null || content.isEmpty()) {
                return null;
            }

            // Extract the second ## section (Manager Quotes)
            String managersQuotesContent = extractSectionContent(content, 1);

            // Extract the third ## section (Player Quotes)
            String playersQuotesContent = extractSectionContent(content, 2);

            // Return null if both quotes are empty
            if ((managersQuotesContent == null || managersQuotesContent.trim().isEmpty()) &&
                    (playersQuotesContent == null || playersQuotesContent.trim().isEmpty())) {
                return null;
            }

            return MatchDetails.Quotes.builder()
                    .managersQuotes(managersQuotesContent != null ? managersQuotesContent.trim() : null)
                    .playersQuotes(playersQuotesContent != null ? playersQuotesContent.trim() : null)
                    .build();

        } catch (Exception e) {
            log.error("Error mapping quotes data: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Extracts content from a specific ## section by index (0-based)
     * @param content The markdown content
     * @param sectionIndex The index of the ## section to extract (0 = first ##, 1 = second ##, etc.)
     * @return The content of the specified section, or null if not found
     */
    private String extractSectionContent(String content, int sectionIndex) {
        if (content == null || content.isEmpty()) {
            return null;
        }

        // Split content by ## headers
        String[] sections = content.split("(?m)^## ");

        // The first element might be empty or contain content before the first ##
        // Adjust index to account for this
        int actualIndex = sectionIndex + 1; // +1 because split creates empty first element

        if (actualIndex >= sections.length) {
            return null;
        }

        String sectionContent = sections[actualIndex];

        // Remove the header line (everything up to the first newline)
        int firstNewlineIndex = sectionContent.indexOf('\n');
        if (firstNewlineIndex != -1) {
            sectionContent = sectionContent.substring(firstNewlineIndex + 1);
        }

        return sectionContent.trim();
    }
}